<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 20 20">
  <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
  <defs>
    <style>
      .st0 {
        fill: none;
      }

      .st1 {
        fill-rule: evenodd;
      }

      .st2 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="st0" width="20" height="20"/>
    </clipPath>
  </defs>
  <g class="st2">
    <path class="st1" d="M10,0c5.523,0,10,4.477,10,10s-4.477,10-10,10S0,15.523,0,10,4.477,0,10,0ZM14.496,6.773c-.867-.385-1.835-.384-2.566.132-.111.079-.022.253.106.204.709-.274,1.611-.209,2.335.414.909.782,1.327,2.282.498,3.271-.273.326-.652.567-1.077.627-.755.107-1.521-.151-2.149-.558-.341-.22-.65-.487-.92-.791-.324-.364-.632-.743-.916-1.139-.478-.667-.886-1.339-1.583-1.804-.572-.38-1.254-.599-1.943-.556-.69.043-1.389.378-1.913.869-.946.888-1.116,2.376-.521,3.532.429.834,1.167,1.432,2,1.825.153.074.253-.103.158-.19-.786-.716-1.514-1.85-1.257-2.772.155-.565.463-.793.944-.788.358.002.832.426,1.062.734.084.113.169.225.257.335.625.785,1.217,1.392,2.494,2.271v.002c2.674,1.935,6.683,1.267,6.984-2.315.097-1.146-.439-2.616-1.993-3.306ZM5.508,9.303c-.521.131-.678.832-.501,1.431l.006.018c.679,1.841,2.861,3.186,4.487,2.247.017-.01.032-.022.045-.036,0,0,0-.001.002-.002.077-.084.063-.218-.032-.281l-.467-.315c-.03-.02-.065-.031-.102-.032-.192-.004-.608-.004-.887-.035-1.491-.13-2.875-1.48-2.454-2.891.018-.06-.037-.117-.098-.103ZM6.083,10.519c-.046-.041-.115.008-.093.066.095.247.332.672.908,1.054.27.178.619.26.998.31.111.014.221.026.328.032.06.003.089-.075.041-.112-.207-.163-.41-.34-.609-.529v-.002c-.066-.062-.153-.097-.244-.095-.491.014-1.057-.477-1.329-.723ZM14.498,9.491c-.07-.092-.194-.123-.297-.066-.173.095-.356.179-.52.28-.165.101-.325.216-.472.339-.272.228-.53.472-.782.72-.085.083-.057.226.053.271.618.252,1.282.154,1.282.154.019-.003.848-.138,1.002-.719.104-.396-.113-.774-.267-.978ZM6.631,10.095c-.428-.538-.812-.816-.812-.171,0,.115.063.222.164.283.402.243.774.632,1.203.744.074.019.127-.066.076-.12-.215-.227-.416-.465-.633-.734v-.002ZM13.966,8.993c-.273-.084-.699-.205-1.327.072-.677.299-1.186.701-1.426.875-.07.051-.082.151-.025.216.126.144.378.388.769.576.062.03.136.014.179-.039.233-.281,1.203-1.135,1.843-1.426.123-.056.116-.235-.013-.274ZM12.931,7.208c-.618.003-1.16.32-1.587.748-.217.218-.401.467-.58.716-.33.462-.46.538-.254.775l.186.215c.104.121.206.182.335.089.299-.213.681-.457.995-.645.35-.207.734-.405,1.148-.439.404-.033.828.071,1.158.303.252.177.458.425.56.719h.001c.035.093.173.076.184-.021.074-.594-.236-1.286-.614-1.723-.379-.437-.939-.738-1.532-.736Z"/>
  </g>
</svg>