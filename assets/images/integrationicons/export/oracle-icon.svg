<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 20 20">
  <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
  <defs>
    <style>
      .st0 {
        fill: none;
      }

      .st1 {
        fill-rule: evenodd;
      }

      .st2 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="st0" width="20" height="20"/>
    </clipPath>
  </defs>
  <g class="st2">
    <g>
      <path d="M3.863,9.408c.095-.003.19.013.278.047.088.034.169.085.237.151.068.066.122.145.159.232.037.087.056.182.056.276s-.019.189-.056.276c-.037.087-.091.167-.159.232-.068.066-.149.117-.237.151-.088.034-.184.05-.278.047h-1.214c-.184-.006-.358-.083-.485-.215-.128-.132-.199-.308-.199-.492,0-.184.071-.36.199-.492.128-.132.302-.209.485-.215h1.214Z"/>
      <path class="st1" d="M10,0c5.523,0,10,4.477,10,10s-4.477,10-10,10S0,15.523,0,10,4.477,0,10,0ZM13.581,10.998c0,.028.006.055.017.08.011.025.026.048.046.067.02.02.044.037.07.048.026.011.055.017.083.017h1.913l.249-.387h-1.959l.002-1.803h-.421v1.977ZM2.622,9.021c-.146-.004-.292.022-.428.075-.136.053-.26.133-.364.234-.105.102-.187.224-.244.358-.057.134-.086.279-.086.425,0,.146.029.29.086.425.057.134.14.257.244.358.104.102.228.182.364.235.136.053.282.078.428.074h1.271c.285-.007.555-.125.754-.329.199-.204.311-.479.311-.764,0-.285-.112-.559-.311-.763-.199-.204-.469-.323-.754-.33h-1.271ZM5.205,11.207h.42v-1.799h1.394c.094,0,.184.037.25.104.066.066.104.156.105.25,0,.094-.038.184-.105.25-.066.066-.156.104-.25.104h-1.185l1.255,1.092h.609l-.843-.707h.189c.196,0,.384-.078.522-.217.139-.139.217-.327.217-.522,0-.196-.078-.385-.217-.523-.139-.139-.327-.217-.522-.217h-1.84v2.186ZM9.381,9c-.05,0-.1.013-.145.036-.045.023-.083.057-.112.099l-1.324,2.072h.495l1.089-1.727.593.956h-1.119l.247.385h1.135l.232.386h.495l-1.327-2.076c-.029-.041-.068-.074-.113-.097-.045-.023-.095-.035-.146-.034ZM11.845,9.021c-.146-.004-.291.022-.427.075-.136.053-.26.133-.364.234-.105.102-.187.224-.244.358-.057.134-.086.279-.086.425,0,.146.029.29.086.425.057.134.14.257.244.358.104.102.228.182.364.235.136.053.281.078.427.074h1.297l.246-.385h-1.514c-.095.003-.19-.013-.278-.047-.088-.034-.169-.085-.237-.151-.068-.066-.122-.145-.159-.232-.037-.087-.056-.181-.056-.276s.019-.189.056-.276c.037-.087.091-.167.159-.232.068-.066.149-.117.237-.151.089-.034.183-.05.278-.047h1.232l.249-.387h-1.51ZM16.956,9.021c-.285.008-.555.126-.754.33-.199.204-.311.479-.311.764,0,.285.112.559.311.763.199.204.469.323.754.33h1.298l.246-.385h-1.516l-.113-.009c-.113-.018-.22-.064-.312-.134-.123-.093-.213-.224-.255-.372h1.797l.247-.386h-2.044c.042-.148.131-.279.254-.372.123-.093.274-.143.428-.143h1.232l.247-.387h-1.509Z"/>
    </g>
  </g>
</svg>