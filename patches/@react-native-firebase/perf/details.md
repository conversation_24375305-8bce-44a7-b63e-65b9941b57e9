# `@react-native-firebase/perf` patches

### [@react-native-firebase+perf+22.2.1+001+hybrid-app.patch](@react-native-firebase+perf+22.2.1+001+hybrid-app.patch)

- Reason: The patch adapts `@react-native-firebase/perf`'s build process to work reliably in our custom environment.
- Upstream PR/issue: Since this is tightly coupled to our specific Gradle configuration, it's not appropriate for upstream inclusion.
- E/App issue: This patch will remain in place to maintain compatibility with our build setup.
- PR introducing patch: N/A