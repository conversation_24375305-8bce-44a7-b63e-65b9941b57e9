diff --git a/node_modules/@react-native-firebase/analytics/android/build.gradle b/node_modules/@react-native-firebase/analytics/android/build.gradle
index 0cc9baa..d6b2595 100644
--- a/node_modules/@react-native-firebase/analytics/android/build.gradle
+++ b/node_modules/@react-native-firebase/analytics/android/build.gradle
@@ -44,6 +44,8 @@ if (coreVersionDetected != coreVersionRequired) {
   }
 }
 
+apply plugin: 'com.android.library'
+
 project.ext {
   set('react-native', [
     versions: [
@@ -175,4 +177,3 @@ dependencies {
 ReactNative.shared.applyPackageVersion()
 ReactNative.shared.applyDefaultExcludes()
 ReactNative.module.applyAndroidVersions()
-ReactNative.module.applyReactNativeDependency("api")
