diff --git a/node_modules/@react-native-firebase/app/android/build.gradle b/node_modules/@react-native-firebase/app/android/build.gradle
index 862ee1d..ea0a070 100644
--- a/node_modules/@react-native-firebase/app/android/build.gradle
+++ b/node_modules/@react-native-firebase/app/android/build.gradle
@@ -19,6 +19,7 @@ buildscript {
 
 plugins {
   id "io.invertase.gradle.build" version "1.5"
+  id 'com.android.library'
 }
 
 def packageJson = PackageJson.getForProject(project)
@@ -101,6 +102,7 @@ repositories {
 }
 
 dependencies {
+  api 'com.facebook.react:react-native:+'
   implementation platform("com.google.firebase:firebase-bom:${ReactNative.ext.getVersion("firebase", "bom")}")
   implementation "com.google.firebase:firebase-common"
   implementation "com.google.android.gms:play-services-auth:${ReactNative.ext.getVersion("play", "play-services-auth")}"
@@ -125,4 +127,3 @@ if ((jvmVersion?.toInteger() ?: 17) < 17) {
 ReactNative.shared.applyPackageVersion()
 ReactNative.shared.applyDefaultExcludes()
 ReactNative.module.applyAndroidVersions()
-ReactNative.module.applyReactNativeDependency("api")
