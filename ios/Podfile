require File.join(File.dirname(`node --print "require.resolve('expo/package.json')"`), "scripts/autolinking")
# Set the type of Mapbox SDK to use
# This value is used by $RNMapboxMaps
$RNMapboxMapsImpl = 'mapbox'
$VCDisableFrameProcessors = true

def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, min_ios_version_supported
prepare_react_native_project!


setup_permissions([
  'Camera',
  'Contacts',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse'
])

# As per the official React Native Firebase documentation: https://rnfirebase.io/#altering-cocoapods-to-use-frameworks
use_frameworks! :linkage => :static
$RNFirebaseAsStaticFramework = true

# Force pods to match minimum iOS version for React Native
# Fixes build issue on Xcode Cloud where some pods
# Use iOS 12 calls despite being set as iOS 11
def __apply_Xcode_14_3_RC_post_install_workaround(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      current_target = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
      minimum_target = min_ios_version_supported
      if current_target.to_f < minimum_target.to_f
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = minimum_target
      end
    end
  end
end

# Pods does not derive :debug type from NewExpensify's debug configurations so we need to set it manually before react-native-post-install. 
# Based on this value react-native-post-install will set the correct GCC_PREPROCESSOR_DEFINITIONS e.g. ENABLE_HERMES_DEBUGGER
def __set_debug_type_for_debug_configurations(installer)
  installer.pods_project.build_configurations.each do |config|
    if config.name.downcase.include?("debug")
      installer.pods_project.add_build_configuration(config.name, :debug)
    end
  end
end

# Configure Mapbox before installing dependencies
pre_install do |installer|
  $RNMapboxMaps.pre_install(installer)
end

target 'NewExpensify' do
  use_expo_modules!
  post_integrate do |installer|
    begin
      expo_patch_react_imports!(installer)
    rescue => e
      Pod::UI.warn e
    end
  end
  permissions_path = '../node_modules/react-native-permissions/ios'

  project 'NewExpensify',
    'DebugDevelopment' => :debug,
    'DebugAdHoc' => :debug,
    'DebugProduction' => :debug,
    'ReleaseDevelopment' => :release,
    'ReleaseAdHoc' => :release,
    'ReleaseProduction' => :release,
    'Debug' => :debug

  config = use_native_modules!(['npx', 'rnef', 'config', '-p', 'ios'])

  # Flags change depending on the env values.
  flags = get_default_flags()

  # ENV Variable enables/disables TurboModules
  ENV['RCT_NEW_ARCH_ENABLED'] = '1';


  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # Configure Mapbox after installation
    $RNMapboxMaps.post_install(installer)

    __set_debug_type_for_debug_configurations(installer)
    # https://github.com/facebook/react-native/blob/main/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    __apply_Xcode_14_3_RC_post_install_workaround(installer)

    installer.pods_project.targets.each do |target|
      if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
        target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
    end
  end
end

target 'NotificationServiceExtension' do
  pod 'AirshipServiceExtension'
  pod 'GzipSwift'
  pod 'AppLogs', :path => '../node_modules/react-native-app-logs/AppLogsPod'
end

pod 'FullStory', :http => 'https://ios-releases.fullstory.com/fullstory-1.61.0-xcframework.tar.gz'
