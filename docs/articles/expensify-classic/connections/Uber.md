---
title: Integrate Uber with Expensify
description: Learn how to connect Uber to Expensify Classic and how trip and tip expenses appear as separate entries based on Uber receipts.
keywords: [Uber integration, Expensify Classic, trip expense, tip expense, connect Uber, import Uber receipts]
---
<div id="expensify-classic" markdown="1">

If you use Uber for business travel, you can automatically forward your ride receipts to Expensify—no manual uploads required!

---

# Connect Uber to Expensify

You can connect your Uber account to Expensify by enrolling in Uber for Business and choosing Expensify as your expense provider.

---

## Option 1: Enroll in Uber for Business and connect Expensify

If you haven't yet enrolled in Uber for Business:

1. Go to **Settings > Account > Wallet**.
2. Scroll down and click **Start using Uber for Business**.
3. Follow the prompts to enroll and choose **Expensify** as your expense provider during setup.

---

## Option 2: Already enrolled in Uber for Business? Just choose Expensify

If you’ve already enrolled in Uber for Business:

1. Go to **Settings > Account > Wallet**.
2. Scroll to the **Ride profiles** section.
3. Select **Business**.
4. Under **Expense provider**, choose **Expensify**.

This links your Uber Business profile to Expensify for automatic receipt forwarding.

---

## What happens after connecting?

- Uber receipts will appear in Expensify just like SmartScanned receipts.
- If you use Scheduled Submit, your Uber receipts will be added to your next report automatically.
- If the ride was paid for with an Expensify Card, the Uber receipt will automatically merge with the corresponding card expense.

---

# How Uber expenses are created

Expensify imports expenses based on the receipts Uber sends:

- **Trip expense:** Created from Uber’s receipt for the base fare, taxes, and fees.
- **Tip expense:** Created from a separate receipt if you tip your driver after the ride.

This helps ensure each charge is clearly tracked and categorized.

---

# FAQ

## Can I connect multiple Uber accounts to Expensify?

No. Each Uber account can only be connected to one Expensify account.

## What happens if I delete the connection?

If you disconnect Uber from Expensify, future receipts will no longer be forwarded automatically. You can reconnect anytime by using either method listed above.

</div>
